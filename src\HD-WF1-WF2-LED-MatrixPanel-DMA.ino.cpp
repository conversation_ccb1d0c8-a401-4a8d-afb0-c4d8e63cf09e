// Custom LED Matrix Firmware (leveraging the HUB75 DMA library) for the Huidu HUB75 Series Control Cards.
// Example shop link: https://www.aliexpress.com/item/1005005038544582.html -> WF1
// Example shop link: https://www.aliexpress.com/item/1005002271988988.html -> WF2

#if defined(WF1)
  #include "hd-wf1-esp32s2-config.h"
#elif defined(WF2)
  #include "hd-wf2-esp32s3-config.h"
#else
  #error "Please define either WF1 or WF2"
#endif  


#include <esp_err.h>
#include <esp_log.h>
#include "debug.h"
#include "littlefs_core.h"
#include <ctime>
#include "driver/ledc.h"

#include <Arduino.h>

#include <WiFi.h>
#include <WiFiMulti.h>
#include <HTTPClient.h>

#include <WebServer.h>
#include <ESPmDNS.h>
#include <I2C_BM8563.h>   // https://github.com/tanakamasayuki/I2C_BM8563

#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <ESP32-VirtualMatrixPanel-I2S-DMA.h>
#include <ElegantOTA.h> // upload firmware by going to http://<ipaddress>/update

#include <ESP32Time.h>
#include <Bounce2.h>

#define fs LittleFS

/*----------------------------- Wifi Configuration -------------------------------*/

const char *wifi_ssid = "Sanbroz";
const char *wifi_pass = "+919814318050";

/*----------------------------- RTC and NTP -------------------------------*/

I2C_BM8563 rtc(I2C_BM8563_DEFAULT_ADDRESS, Wire1);
const char* ntpServer         = "time.cloudflare.com";
const char* ntpLastUpdate     = "/ntp_last_update.txt";

// NTP Clock Offset / Timezone
#define CLOCK_GMT_OFFSET 1

/*-------------------------- HUB75E DMA Setup -----------------------------*/
#define PANEL_RES_X 32    // Number of pixels wide of each INDIVIDUAL panel module. 
#define PANEL_RES_Y 32     // Number of pixels tall of each INDIVIDUAL panel module.
#define PANEL_CHAIN 1      // Total number of panels chained one to another


#if defined(WF1)

HUB75_I2S_CFG::i2s_pins _pins_x1 = {WF1_R1_PIN, WF1_G1_PIN, WF1_B1_PIN, WF1_R2_PIN, WF1_G2_PIN, WF1_B2_PIN, WF1_A_PIN, WF1_B_PIN, WF1_C_PIN, WF1_D_PIN, WF1_E_PIN, WF1_LAT_PIN, WF1_OE_PIN, WF1_CLK_PIN};

#else

HUB75_I2S_CFG::i2s_pins _pins_x1 = {WF2_X1_R1_PIN, WF2_X1_G1_PIN, WF2_X1_B1_PIN, WF2_X1_R2_PIN, WF2_X1_G2_PIN, WF2_X1_B2_PIN, WF2_A_PIN, WF2_B_PIN, WF2_C_PIN, WF2_D_PIN, WF2_X1_E_PIN, WF2_LAT_PIN, WF2_OE_PIN, WF2_CLK_PIN};
HUB75_I2S_CFG::i2s_pins _pins_x2 = {WF2_X2_R1_PIN, WF2_X2_G1_PIN, WF2_X2_B1_PIN, WF2_X2_R2_PIN, WF2_X2_G2_PIN, WF2_X2_B2_PIN, WF2_A_PIN, WF2_B_PIN, WF2_C_PIN, WF2_D_PIN, WF2_X2_E_PIN, WF2_LAT_PIN, WF2_OE_PIN, WF2_CLK_PIN};

#endif


/*-------------------------- Class Instances ------------------------------*/
// Routing in the root page and webcamview.html natively uses the request
// handlers of the ESP32 WebServer class, so it explicitly instantiates the
// ESP32 WebServer.
WebServer           webServer;
WiFiMulti           wifiMulti;
ESP32Time           esp32rtc;  // offset in seconds GMT+1
MatrixPanel_I2S_DMA *dma_display = nullptr;
VirtualMatrixPanel  *virtualDisp = nullptr;

// INSTANTIATE A Button OBJECT FROM THE Bounce2 NAMESPACE
Bounce2::Button button = Bounce2::Button();

// ROS Task management
TaskHandle_t Task1;
TaskHandle_t Task2;

#include "led_pwm_handler.h"
#include <HardwareSerial.h>

RTC_DATA_ATTR int bootCount = 0;

volatile bool buttonPressed = false;
int currentConfig = 0;
const int maxConfigs = 7;

// Panel configuration structure
struct PanelConfig {
    int width;
    int height;
    const char* name;
    const char* description;
};

// Different panel configurations to test
PanelConfig panelConfigs[maxConfigs] = {
    {32, 32, "1/16 Scan", "Standard 32x32 1/16 scan"},
    {64, 16, "1/8 Scan A", "32x32 panel as 64x16 (simple mapping)"},
    {64, 16, "1/8 Scan B", "32x32 panel as 64x16 (split mapping)"},
    {64, 16, "1/8 Scan C", "32x32 panel as 64x16 (interleaved)"},
    {32, 16, "1/4 Scan", "32x32 panel as 32x16"},
    {128, 8, "1/8 Scan D", "32x32 panel as 128x8"},
    {128, 32, "4x2 Panels", "32x32 as 4 rows x 2 cols of 16x8 panels"}
};

// Function declarations
void initializeDisplay(int configIndex);
void displayTestPattern();
void drawPixelMapped(int16_t x, int16_t y, uint16_t color);
void clearDisplay();
void fillScreenMapped(uint16_t color);
void drawFastVLineMapped(int16_t x, int16_t y, int16_t h, uint16_t color);
void drawFastHLineMapped(int16_t x, int16_t y, int16_t w, uint16_t color);
void drawRectMapped(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color);
void drawChar(int16_t x, int16_t y, int charIndex, uint16_t color);

IRAM_ATTR void toggleButtonPressed() {
  // This function will be called when the interrupt occurs on pin PUSH_BUTTON_PIN
  buttonPressed = true;
}



/*
Method to print the reason by which ESP32
has been awaken from sleep
*/
// Removed debug function


// Function that gets current epoch time
unsigned long getEpochTime() {
  time_t now;
  struct tm timeinfo;
  if (!getLocalTime(&timeinfo)) {
    //Serial.println("Failed to obtain time");
    return(0);
  }
  time(&now);
  return now;
}

//
// Arduino Setup Task
//
void setup() {

  // Minimal initialization - no serial debug

  /*-------------------- START THE HUB75E DISPLAY --------------------*/

    // Initialize with first configuration
    initializeDisplay(currentConfig);


  /*-------------------- START THE NETWORKING --------------------*/
  WiFi.mode(WIFI_STA);
  wifiMulti.addAP(wifi_ssid, wifi_pass); // configure in the *-config.h file

  // wait for WiFi connection (silent)
  while (wifiMulti.run() != WL_CONNECTED) {
    delay(100);
  }
    

  /*-------------------- --------------- --------------------*/
  //Increment boot number
  ++bootCount;

  esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();    

  // Skip wake up messages


  /*
    We set our ESP32 to wake up for an external trigger.
    There are two types for ESP32, ext0 and ext1 .
  */
  esp_sleep_enable_ext0_wakeup((gpio_num_t)PUSH_BUTTON_PIN, 0); //1 = High, 0 = Low  

  /*-------------------- --------------- --------------------*/
  // BUTTON SETUP 
  button.attach( PUSH_BUTTON_PIN, INPUT ); // USE EXTERNAL PULL-UP
  button.interval(5);   // DEBOUNCE INTERVAL IN MILLISECONDS
  button.setPressedState(LOW); // INDICATE THAT THE LOW STATE CORRESPONDS TO PHYSICALLY PRESSING THE BUTTON


  /*-------------------- LEDC Controller --------------------*/
    // Prepare and then apply the LEDC PWM timer configuration
    ledc_timer_config_t ledc_timer = {
        .speed_mode       = LEDC_LOW_SPEED_MODE,
        .duty_resolution  = LEDC_TIMER_13_BIT ,
        .timer_num        = LEDC_TIMER_0,
        .freq_hz          = 4000,  // Set output frequency at 4 kHz
        .clk_cfg          = LEDC_AUTO_CLK
    };
    ESP_ERROR_CHECK(ledc_timer_config(&ledc_timer));

    // Prepare and then apply the LEDC PWM channel configuration
    ledc_channel_config_t ledc_channel = {
        .gpio_num       = RUN_LED_PIN,
        .speed_mode     = LEDC_LOW_SPEED_MODE,
        .channel        = LEDC_CHANNEL_0,
        .intr_type      = LEDC_INTR_DISABLE,
        .timer_sel      = LEDC_TIMER_0,
        .duty           = 0, // Set duty to 0%
        .hpoint         = 0
    };
    ESP_ERROR_CHECK(ledc_channel_config(&ledc_channel));  


    // Start fading that LED
    xTaskCreatePinnedToCore(
      ledFadeTask,            /* Task function. */
      "ledFadeTask",                 /* name of task. */
      1000,                    /* Stack size of task */
      NULL,                     /* parameter of the task */
      1,                        /* priority of the task */
      &Task1,                   /* Task handle to keep track of created task */
      0);                       /* Core */   
    

  /*-------------------- INIT LITTLE FS --------------------*/
  if(!LittleFS.begin(FORMAT_LITTLEFS_IF_FAILED)){
      return;
  }
 
  /*-------------------- --------------- --------------------*/
  // Init I2C for RTC
  Wire1.begin(BM8563_I2C_SDA, BM8563_I2C_SCL);
  rtc.begin();

  // Get RTC date and time
  I2C_BM8563_DateTypeDef rtcDate;
  I2C_BM8563_TimeTypeDef rtcTime;
  rtc.getDate(&rtcDate);
  rtc.getTime(&rtcTime);
  
  time_t ntp_last_update_ts = 0;
  File file = fs.open(ntpLastUpdate, FILE_READ, true);
  if(!file) {
      // File doesn't exist, continue
  } else  {
      file.read( (uint8_t*) &ntp_last_update_ts, sizeof(ntp_last_update_ts));
      file.close();
  }

  // Current RTC
  std::tm curr_rtc_tm = make_tm(rtcDate.year, rtcDate.month, rtcDate.date);    // April 2nd, 2012
  time_t  curr_rtc_ts = std::mktime(&curr_rtc_tm);

  if ( std::abs( (long int) (curr_rtc_ts - ntp_last_update_ts)) > (60*60*24*30) && (bootCount == 0))
  {
      // Update RTC from Internet NTP (silent)

      // Set ntp time to local
      configTime(CLOCK_GMT_OFFSET * 3600, 0, ntpServer);

      // Get local time
      struct tm timeInfo;
      if (getLocalTime(&timeInfo)) {
        // Set RTC time
        I2C_BM8563_TimeTypeDef timeStruct;
        timeStruct.hours   = timeInfo.tm_hour;
        timeStruct.minutes = timeInfo.tm_min;
        timeStruct.seconds = timeInfo.tm_sec;
        rtc.setTime(&timeStruct);

        // Set RTC Date
        I2C_BM8563_DateTypeDef dateStruct;
        dateStruct.weekDay = timeInfo.tm_wday;
        dateStruct.month   = timeInfo.tm_mon + 1;
        dateStruct.date    = timeInfo.tm_mday;
        dateStruct.year    = timeInfo.tm_year + 1900;
        rtc.setDate(&dateStruct);
    }

      ntp_last_update_ts = getEpochTime();
      File file = fs.open(ntpLastUpdate, FILE_WRITE);
      if(!file) {
          // Failed to open file
      } else  {
          file.write( (uint8_t*) &ntp_last_update_ts, sizeof(ntp_last_update_ts));
          file.close();
      }

  } else {
    esp32rtc.setTime(rtcTime.seconds, rtcTime.minutes, rtcTime.hours, rtcDate.date, rtcDate.month, rtcDate.year);  // 17th Jan 2021 15:24:30
  }

   /*-------------------- --------------- --------------------*/

    webServer.on("/", []() {
      webServer.send(200, "text/plain", "Hi! I am here.");
    });

    ElegantOTA.begin(&webServer);    // Start ElegantOTA
    webServer.begin();

    /*-------------------- --------------- --------------------*/
    delay(1000);

    virtualDisp->clearScreen();

}

unsigned long last_update = 0;

// Function to initialize display with specific configuration
void initializeDisplay(int configIndex) {
    if (dma_display != nullptr) {
        delete dma_display;
        dma_display = nullptr;
    }

    PanelConfig config = panelConfigs[configIndex];

    HUB75_I2S_CFG mxconfig(
        config.width,      // Panel width
        config.height,     // Panel height
        PANEL_CHAIN,       // Chain length
        _pins_x1           // Pin mapping
    );

    // Common settings for all configurations
    mxconfig.i2sspeed = HUB75_I2S_CFG::HZ_8M;
    mxconfig.latch_blanking = 1;
    mxconfig.clkphase = false;
    mxconfig.min_refresh_rate = 60;

    dma_display = new MatrixPanel_I2S_DMA(mxconfig);
    dma_display->begin();
    dma_display->setBrightness8(128);
    dma_display->clearScreen();
}

// Different pixel mapping functions for different scan types
void drawPixelMapped(int16_t x, int16_t y, uint16_t color) {
    if (x < 0 || x >= 32 || y < 0 || y >= 32) return;

    int16_t new_x, new_y;

    switch(currentConfig) {
        case 0: // Standard 1/16 scan (32x32)
            new_x = x;
            new_y = y;
            break;

        case 1: // 1/8 Scan A - Simple horizontal split
            if (y < 16) {
                new_x = x;
                new_y = y;
            } else {
                new_x = x + 32;
                new_y = y - 16;
            }
            break;

        case 2: // 1/8 Scan B - Vertical interleave
            new_x = (y % 2) * 32 + x;
            new_y = y / 2;
            break;

        case 3: // 1/8 Scan C - Row interleave (every 8th row)
            new_x = x + (y % 8) * 8;
            new_y = y / 8;
            break;

        case 4: // 1/4 Scan (32x16)
            new_x = x;
            new_y = y / 2;
            break;

        case 5: // 1/8 Scan D - Extreme horizontal split (128x8)
            new_x = x + (y / 8) * 32;
            new_y = y % 8;
            break;

        case 6: // 4x2 Panel Layout - 32x32 divided into 4 rows x 2 cols of 16x8 sub-panels
            {
                // Each sub-panel is 16x8 pixels
                // Panel layout:
                // [0,0] [0,1]  (top row)
                // [1,0] [1,1]
                // [2,0] [2,1]
                // [3,0] [3,1]  (bottom row)

                int panel_row = y / 8;      // Which row of panels (0-3)
                int panel_col = x / 16;     // Which column of panels (0-1)
                int local_x = x % 16;       // X within the sub-panel (0-15)
                int local_y = y % 8;        // Y within the sub-panel (0-7)

                // Map to linear buffer: each panel gets 16*8=128 pixels
                // Panel [r,c] starts at position: (r*2 + c) * 128
                int panel_index = panel_row * 2 + panel_col;
                new_x = (panel_index * 16) + local_x;
                new_y = local_y;
            }
            break;

        default:
            new_x = x;
            new_y = y;
            break;
    }

    dma_display->drawPixel(new_x, new_y, color);
}

// Helper function to clear the display
void clearDisplay() {
    dma_display->clearScreen();
}

// Helper function to fill screen with color
void fillScreenMapped(uint16_t color) {
    for (int y = 0; y < 32; y++) {
        for (int x = 0; x < 32; x++) {
            drawPixelMapped(x, y, color);
        }
    }
}

// Helper function to draw vertical line
void drawFastVLineMapped(int16_t x, int16_t y, int16_t h, uint16_t color) {
    for (int16_t i = 0; i < h; i++) {
        drawPixelMapped(x, y + i, color);
    }
}

// Helper function to draw horizontal line
void drawFastHLineMapped(int16_t x, int16_t y, int16_t w, uint16_t color) {
    for (int16_t i = 0; i < w; i++) {
        drawPixelMapped(x + i, y, color);
    }
}

// Helper function to draw rectangle
void drawRectMapped(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color) {
    drawFastHLineMapped(x, y, w, color);           // Top
    drawFastHLineMapped(x, y + h - 1, w, color);   // Bottom
    drawFastVLineMapped(x, y, h, color);           // Left
    drawFastVLineMapped(x + w - 1, y, h, color);   // Right
}

// Simple 5x7 font for "TEST" text
const uint8_t font5x7[4][7] = {
    {0x7C, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10}, // T
    {0x7C, 0x40, 0x40, 0x78, 0x40, 0x40, 0x7C}, // E
    {0x3C, 0x40, 0x40, 0x3C, 0x02, 0x02, 0x7C}, // S
    {0x7C, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10}  // T
};

// Function to draw a character using the 5x7 font
void drawChar(int16_t x, int16_t y, int charIndex, uint16_t color) {
    for (int row = 0; row < 7; row++) {
        uint8_t line = font5x7[charIndex][row];
        for (int col = 0; col < 8; col++) {
            if (line & (0x80 >> col)) {
                drawPixelMapped(x + col, y + row, color);
            }
        }
    }
}

// Function to display TEST text and configuration info
void displayTestPattern() {
    clearDisplay();

    // Draw "TEST" text in white, centered
    uint16_t white = dma_display->color565(255, 255, 255);
    int startX = 2; // Start position for centering "TEST" (4 chars * 6 pixels wide = 24, (32-24)/2 = 4)
    int startY = 8; // Vertical center

    drawChar(startX, startY, 0, white);      // T
    drawChar(startX + 6, startY, 1, white); // E
    drawChar(startX + 12, startY, 2, white);// S
    drawChar(startX + 18, startY, 0, white);// T

    // Draw border to show panel boundaries
    drawRectMapped(0, 0, 32, 32, dma_display->color565(0, 255, 0)); // Green border

    // Draw corner markers
    drawPixelMapped(0, 0, dma_display->color565(255, 0, 0));     // Red top-left
    drawPixelMapped(31, 0, dma_display->color565(255, 0, 0));    // Red top-right
    drawPixelMapped(0, 31, dma_display->color565(255, 0, 0));    // Red bottom-left
    drawPixelMapped(31, 31, dma_display->color565(255, 0, 0));   // Red bottom-right

    // Draw center cross
    drawPixelMapped(15, 16, dma_display->color565(0, 0, 255));   // Blue center
    drawPixelMapped(16, 16, dma_display->color565(0, 0, 255));   // Blue center
    drawPixelMapped(16, 15, dma_display->color565(0, 0, 255));   // Blue center
    drawPixelMapped(16, 17, dma_display->color565(0, 0, 255));   // Blue center
}

void loop()
{
    // Handle button press to cycle through configurations
    button.update();
    if ( button.pressed() ) {
        currentConfig = (currentConfig + 1) % maxConfigs;

        // Reinitialize display with new configuration
        initializeDisplay(currentConfig);

        // Display test pattern
        displayTestPattern();

        delay(500); // Longer debounce delay to see the change
    }

    // Handle web server
    webServer.handleClient();
    delay(1);

    // Display initial test pattern on first run
    if ( (millis() - last_update) > 2000 && last_update == 0) {
        displayTestPattern();
        last_update = millis();
    }
} // loop()